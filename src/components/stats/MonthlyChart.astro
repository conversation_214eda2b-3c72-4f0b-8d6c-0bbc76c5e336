---
import type { MonthlyStats } from "@/data/post";

export interface Props {
	data: MonthlyStats[];
	title?: string;
}

const { data, title = "Monthly Posts" } = Astro.props;

// Calculate max value for scaling
const maxCount = Math.max(...data.map(d => d.count), 1);

// Get last 12 months of data
const last12Months = data.slice(-12);
---

<div class="bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700 p-4 sm:p-6">
	<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{title}</h3>
	
	{last12Months.length > 0 ? (
		<div class="space-y-4">
			<!-- Debug info -->
			<div class="text-xs text-gray-500 mb-2">
				Max: {maxCount} | Data: {last12Months.map(m => {
					const heightPercent = m.count > 0 ? Math.max((m.count / maxCount) * 100, 3) : 1;
					const heightPx = Math.round((heightPercent / 100) * 128);
					return `${m.monthName}:${m.count}(${heightPx}px)`;
				}).join(', ')}
			</div>

			<!-- Chart bars -->
			<div class="flex items-end justify-between h-32 gap-1">
				{last12Months.map((month: MonthlyStats) => {
					const heightPercent = month.count > 0 ? Math.max((month.count / maxCount) * 100, 3) : 1;
					const heightPx = Math.round((heightPercent / 100) * 128); // 128px = h-32
					return (
						<div class="flex-1 flex flex-col items-center">
							<div
								class="w-full chart-bar rounded-t"
								style={`height: ${heightPx}px; background-color: var(--color-accent);`}
								title={`${month.monthName} ${month.year}: ${month.count} posts (${heightPercent.toFixed(1)}% = ${heightPx}px)`}
							></div>
						</div>
					);
				})}
			</div>
			
			<!-- Month labels -->
			<div class="flex justify-between text-xs text-gray-600 dark:text-gray-400">
				{last12Months.map((month: MonthlyStats) => (
					<span class="flex-1 text-center">
						{month.monthName}
					</span>
				))}
			</div>
			
			<!-- Year labels (only show when year changes) -->
			<div class="flex justify-between text-xs text-gray-500 dark:text-gray-500">
				{last12Months.map((month: MonthlyStats, index: number) => {
					const showYear = index === 0 || month.year !== last12Months[index - 1]?.year;
					return (
						<span class="flex-1 text-center">
							{showYear ? month.year : ''}
						</span>
					);
				})}
			</div>
		</div>
	) : (
		<div class="text-center py-8 text-gray-500 dark:text-gray-400">
			<p>No data available</p>
		</div>
	)}
</div>

<style>
	.chart-bar {
		transition: all 0.2s ease;
		/* Force override any inherited styles */
		min-height: unset !important;
		max-height: unset !important;
		flex: none !important;
	}

	.chart-bar:hover {
		opacity: 0.8;
	}
</style>
